#!/usr/bin/env python3
# Copyright (c) 2023 Your Organization
# Licensed under the Apache License, Version 2.0

"""
环境检测节点，用于识别机器人当前是在室内还是室外环境。
基于出入口位置和当前环境状态进行判断。
支持识别具体的室内区域和出入口。
使用双门线拓扑关系方法进行环境切换判断。
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import NavSatFix
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseWithCovarianceStamped, Point
import numpy as np
import math
import yaml
import os
import json
from enum import Enum
from dataclasses import dataclass
from nav2_system_tests.msg import EnvInfo
from std_srvs.srv import SetBool

# 环境状态枚举
class EnvironmentState(Enum):
    UNKNOWN = "unknown"
    OUTDOOR = "outdoor"
    ENTERING_INDOOR = "entering_indoor"  # 正在进入室内（穿过外门线，尚未穿过内门线）
    INDOOR = "indoor"
    EXITING_INDOOR = "exiting_indoor"    # 正在离开室内（穿过内门线，尚未穿过外门线）

# 门线数据结构
@dataclass
class DoorLine:
    start: Point  # 门线起点
    end: Point    # 门线终点

# 入口数据结构
@dataclass
class Entrance:
    id: str                  # 入口ID
    name: str                # 入口名称
    inner_point: Point       # 室内侧点(P1)
    outer_point: Point       # 室外侧点(P2)
    inner_door_line: DoorLine  # 内门线
    outer_door_line: DoorLine  # 外门线
    door_width: float        # 门线宽度
    area_id: str             # 所属区域ID


class EnvironmentDetectorNode(Node):
    """
    环境检测节点，用于识别机器人当前是在室内还是室外环境。
    基于出入口位置和当前环境状态进行判断，当机器人接近出入口时自动切换环境。
    """

    def __init__(self):
        """初始化环境检测节点。"""
        super().__init__('environment_detector')

        # 声明并获取参数
        self.declare_parameter('check_interval', 1.0)
        self.declare_parameter('indoor_areas_config', '')
        self.declare_parameter('entrance_detection_radius', 2.0)
        self.declare_parameter('state_file_path', '/tmp/environment_state.json')
        self.declare_parameter('position_change_threshold', 50.0)  # 位置变化阈值（米）
        self.declare_parameter('force_check_on_startup', True)     # 启动时是否强制检查环境
        self.declare_parameter('debug_level', 'info')              # 调试级别: debug, info, warn
        self.declare_parameter('trajectory_buffer_size', 10)       # 轨迹缓冲区大小

        # 获取参数值
        self.check_interval = self.get_parameter('check_interval').value
        self.indoor_areas_config = self.get_parameter('indoor_areas_config').value
        self.entrance_detection_radius = self.get_parameter('entrance_detection_radius').value
        self.state_file_path = self.get_parameter('state_file_path').value
        self.position_change_threshold = self.get_parameter('position_change_threshold').value
        self.force_check_on_startup = self.get_parameter('force_check_on_startup').value
        self.debug_level = self.get_parameter('debug_level').value
        self.trajectory_buffer_size = self.get_parameter('trajectory_buffer_size').value

        # 设置日志级别
        if self.debug_level == 'debug':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.DEBUG)
        elif self.debug_level == 'info':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)
        elif self.debug_level == 'warn':
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.WARN)
        else:
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)

        # 加载室内区域和出入口定义
        self.indoor_areas_config_data = self.load_indoor_areas_config()

        # 创建室内区域ID到区域数据的映射，方便快速查找
        self.indoor_areas_by_id = {}
        for area in self.indoor_areas_config_data:
            if 'id' in area:
                self.indoor_areas_by_id[area['id']] = area

        # 解析入口配置，计算门线
        self.entrances_by_id = {}  # 入口ID到入口对象的映射
        self.parse_entrances()

        # 创建订阅者
        self.gps_sub = self.create_subscription(
            NavSatFix,
            'gps/fix',
            self.gps_callback,
            10)

        self.global_pose_sub = self.create_subscription(
            Odometry,
            'odometry/global',
            self.global_pose_callback,
            10)

        # 创建发布者
        self.env_info_pub = self.create_publisher(
            EnvInfo,
            'environment_info',
            10)

        # 创建服务
        self.set_indoor_srv = self.create_service(
            SetBool,
            'set_indoor_environment',
            self.set_indoor_environment_callback)

        self.set_outdoor_srv = self.create_service(
            SetBool,
            'set_outdoor_environment',
            self.set_outdoor_environment_callback)

        # 初始化状态变量
        self.current_state = EnvironmentState.UNKNOWN  # 使用枚举类型
        self.current_environment = self.current_state.value  # 当前环境字符串，用于发布
        self.current_area_id = ''
        self.current_entrance_id = ''
        self.gps_available = False
        self.last_gps_msg = None
        self.current_position = None
        self.last_saved_position = None  # 上次保存状态时的位置

        # 轨迹记录
        self.trajectory_buffer = []  # 存储最近的位置点
        self.last_crossing_check_position = None  # 上次检查穿越的位置

        # 尝试加载上次保存的环境状态
        self.load_environment_state()

        # 创建定时器，定期检查环境
        self.timer = self.create_timer(self.check_interval, self.check_environment)

        self.get_logger().info('Environment detector node initialized with dual door line topology method')
        self.get_logger().info('Manual environment control services available:')
        self.get_logger().info('  - /set_indoor_environment (std_srvs/SetBool)')
        self.get_logger().info('  - /set_outdoor_environment (std_srvs/SetBool)')

    def load_indoor_areas_config(self):
        """加载室内区域配置文件。"""
        if not self.indoor_areas_config:
            self.get_logger().info('No indoor areas config file specified')
            return []

        try:
            if not os.path.exists(self.indoor_areas_config):
                self.get_logger().error(f'Indoor areas config file not found: {self.indoor_areas_config}')
                return []

            with open(self.indoor_areas_config, 'r') as f:
                config = yaml.safe_load(f)

            self.get_logger().info(f'Loaded indoor areas config from {self.indoor_areas_config}')
            return config.get('indoor_areas', [])
        except Exception as e:
            self.get_logger().error(f'Failed to load indoor areas config: {e}')
            return []

    def calculate_gps_distance(self, lat1, lon1, lat2, lon2):
        """计算两个GPS坐标之间的距离（米）。"""
        # 地球半径（米）
        R = 6371000.0

        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad

        # Haversine公式
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        return distance

    def gps_callback(self, msg):
        """处理GPS消息回调。"""
        self.last_gps_msg = msg

        # 检查GPS是否有效
        if not math.isnan(msg.latitude) and not math.isnan(msg.longitude):
            self.gps_available = True
        else:
            self.gps_available = False
            self.get_logger().debug('Received invalid GPS data (NaN values)')

    def global_pose_callback(self, msg):
        """处理全局位姿消息回调。"""
        # 保存当前位置
        self.current_position = msg.pose.pose

        # 更新轨迹缓冲区
        self.update_trajectory_buffer(self.current_position)

        # 检查是否穿越门线
        self.check_door_line_crossing()

    def update_trajectory_buffer(self, position):
        """更新轨迹缓冲区。"""
        # 添加当前位置到轨迹缓冲区
        self.trajectory_buffer.append(position)

        # 保持缓冲区大小不超过设定值
        if len(self.trajectory_buffer) > self.trajectory_buffer_size:
            self.trajectory_buffer.pop(0)

    def parse_entrances(self):
        """解析入口配置，计算门线。"""
        self.get_logger().info('Parsing entrances and calculating door lines')

        for area in self.indoor_areas_config_data:
            area_id = area.get('id', '')
            if not area_id:
                continue

            for entrance_config in area.get('entrances', []):
                entrance_id = entrance_config.get('id', '')
                if not entrance_id:
                    continue

                # 检查是否使用新的入口点格式
                if 'points' in entrance_config:
                    try:
                        # 获取入口名称
                        name = entrance_config.get('name', f'Entrance {entrance_id}')

                        # 获取门线宽度
                        door_width = entrance_config.get('door_width', 2.0)

                        # 获取室内侧点和室外侧点
                        inner_point_config = entrance_config['points']['inner']['global_pose']
                        outer_point_config = entrance_config['points']['outer']['global_pose']

                        # 创建点对象
                        inner_point = Point()
                        inner_point.x = float(inner_point_config['x'])
                        inner_point.y = float(inner_point_config['y'])
                        inner_point.z = 0.0

                        outer_point = Point()
                        outer_point.x = float(outer_point_config['x'])
                        outer_point.y = float(outer_point_config['y'])
                        outer_point.z = 0.0

                        # 计算门线
                        inner_door_line, outer_door_line = self.calculate_door_lines(inner_point, outer_point, door_width)

                        # 创建入口对象
                        entrance = Entrance(
                            id=entrance_id,
                            name=name,
                            inner_point=inner_point,
                            outer_point=outer_point,
                            inner_door_line=inner_door_line,
                            outer_door_line=outer_door_line,
                            door_width=door_width,
                            area_id=area_id
                        )

                        # 添加到映射中
                        self.entrances_by_id[entrance_id] = entrance

                        self.get_logger().info(f'Added entrance: {name} (ID: {entrance_id}) for area: {area_id}')
                        self.get_logger().debug(f'  Inner point: ({inner_point.x}, {inner_point.y})')
                        self.get_logger().debug(f'  Outer point: ({outer_point.x}, {outer_point.y})')
                        self.get_logger().debug(f'  Door width: {door_width}m')

                    except KeyError as e:
                        self.get_logger().error(f'Failed to parse entrance {entrance_id}: Missing key {e}')
                    except Exception as e:
                        self.get_logger().error(f'Failed to parse entrance {entrance_id}: {e}')

        self.get_logger().info(f'Parsed {len(self.entrances_by_id)} entrances')

    def calculate_door_lines(self, inner_point, outer_point, door_width):
        """计算内外门线。

        Args:
            inner_point: 室内侧点(P1)
            outer_point: 室外侧点(P2)
            door_width: 门线宽度

        Returns:
            tuple: (内门线, 外门线)，每个门线是一个DoorLine对象
        """
        # 计算P1P2的方向向量
        dir_x = outer_point.x - inner_point.x
        dir_y = outer_point.y - inner_point.y

        # 计算方向向量的长度
        dir_length = math.sqrt(dir_x * dir_x + dir_y * dir_y)

        # 如果两点重合，使用默认方向
        if dir_length < 0.001:
            self.get_logger().warning('Inner and outer points are too close, using default direction')
            dir_x = 1.0
            dir_y = 0.0
            dir_length = 1.0

        # 归一化方向向量
        dir_x /= dir_length
        dir_y /= dir_length

        # 计算垂直于P1P2的单位向量
        perp_x = -dir_y
        perp_y = dir_x

        # 计算门线的一半宽度
        half_width = door_width / 2.0

        # 计算内门线的起点和终点
        inner_start = Point()
        inner_start.x = inner_point.x - perp_x * half_width
        inner_start.y = inner_point.y - perp_y * half_width
        inner_start.z = 0.0

        inner_end = Point()
        inner_end.x = inner_point.x + perp_x * half_width
        inner_end.y = inner_point.y + perp_y * half_width
        inner_end.z = 0.0

        # 计算外门线的起点和终点
        outer_start = Point()
        outer_start.x = outer_point.x - perp_x * half_width
        outer_start.y = outer_point.y - perp_y * half_width
        outer_start.z = 0.0

        outer_end = Point()
        outer_end.x = outer_point.x + perp_x * half_width
        outer_end.y = outer_point.y + perp_y * half_width
        outer_end.z = 0.0

        # 创建门线对象
        inner_door_line = DoorLine(start=inner_start, end=inner_end)
        outer_door_line = DoorLine(start=outer_start, end=outer_end)

        return inner_door_line, outer_door_line

    def check_entrance_proximity(self):
        """检查是否接近某个出入口。"""
        # 检查GPS是否可用，是否有最新的GPS消息，以及是否有室内区域配置数据
        if not self.gps_available or self.last_gps_msg is None or not self.indoor_areas_config_data:
            # 如果任何一个条件不满足，则无法进行出入口检测，返回None
            return None, None

        for area in self.indoor_areas_config_data:
            # 检查是否接近某个出入口
            for entrance in area.get('entrances', []):
                # 只支持新的入口点格式
                if 'points' not in entrance:
                    self.get_logger().warning(f'Entrance in area {area.get("id", "unknown")} does not use the new format with points')
                    continue

                # 获取室内侧点和室外侧点的GPS坐标
                if 'gps' not in entrance['points']['inner'] or 'gps' not in entrance['points']['outer']:
                    self.get_logger().warning(f'Entrance {entrance.get("id", "unknown")} missing GPS coordinates')
                    continue

                # 计算到入口中点的距离
                inner_lat = entrance['points']['inner']['gps']['lat']
                inner_lon = entrance['points']['inner']['gps']['lon']
                outer_lat = entrance['points']['outer']['gps']['lat']
                outer_lon = entrance['points']['outer']['gps']['lon']

                # 计算入口中点坐标
                entrance_lat = (inner_lat + outer_lat) / 2.0
                entrance_lon = (inner_lon + outer_lon) / 2.0

                distance = self.calculate_gps_distance(
                    self.last_gps_msg.latitude, self.last_gps_msg.longitude,
                    entrance_lat, entrance_lon
                )

                # 使用出入口自己的半径或默认半径
                radius = entrance.get('radius', self.entrance_detection_radius)

                # 如果在触发半径内，返回区域和出入口信息
                if distance <= radius:
                    self.get_logger().info(f'Near entrance: {entrance["name"]} of {area["name"]}, distance: {distance:.2f}m')
                    return area['id'], entrance['id']

        return None, None

    def save_environment_state(self):
        """保存当前环境状态到文件。"""
        if not self.state_file_path:
            return

        try:
            # 准备要保存的状态数据
            state_data = {
                'environment': self.current_environment,
                'state': self.current_state.value,
                'area_id': self.current_area_id,
                'entrance_id': self.current_entrance_id,
                'position': None
            }

            # 如果有GPS位置，保存它
            if self.last_gps_msg is not None and self.gps_available:
                state_data['position'] = {
                    'latitude': self.last_gps_msg.latitude,
                    'longitude': self.last_gps_msg.longitude
                }
                self.last_saved_position = (self.last_gps_msg.latitude, self.last_gps_msg.longitude)

            # 保存到文件
            with open(self.state_file_path, 'w') as f:
                json.dump(state_data, f)

            self.get_logger().debug(f'Environment state saved to {self.state_file_path}')
        except Exception as e:
            self.get_logger().error(f'Failed to save environment state: {e}')

    def load_environment_state(self):
        """从文件加载环境状态。"""
        if not self.state_file_path or not os.path.exists(self.state_file_path):
            self.get_logger().info('No saved environment state found')
            return

        try:
            # 从文件加载状态
            with open(self.state_file_path, 'r') as f:
                state_data = json.load(f)

            # 设置环境状态
            env_str = state_data.get('environment', 'unknown')
            state_str = state_data.get('state', 'unknown')

            # 尝试将字符串转换为枚举值
            try:
                self.current_state = EnvironmentState(state_str)
            except ValueError:
                self.current_state = EnvironmentState.UNKNOWN
                self.get_logger().warning(f'Invalid state value: {state_str}, using UNKNOWN')

            self.current_environment = env_str
            self.current_area_id = state_data.get('area_id', '')
            self.current_entrance_id = state_data.get('entrance_id', '')

            # 设置上次保存的位置
            position = state_data.get('position')
            if position:
                self.last_saved_position = (position['latitude'], position['longitude'])

            self.get_logger().info(f'Loaded environment state: {self.current_environment} (state: {self.current_state.value})')
            if self.current_area_id:
                self.get_logger().info(f'Loaded area ID: {self.current_area_id}')
            if self.current_entrance_id:
                self.get_logger().info(f'Loaded entrance ID: {self.current_entrance_id}')

        except Exception as e:
            self.get_logger().error(f'Failed to load environment state: {e}')
            # 如果加载失败，使用默认值
            self.current_state = EnvironmentState.UNKNOWN
            self.current_environment = self.current_state.value
            self.current_area_id = ''
            self.current_entrance_id = ''

    def check_door_line_crossing(self):
        """检查机器人是否穿越了门线，并根据穿越情况更新环境状态。"""
        # 如果轨迹缓冲区为空或只有一个点，无法检测穿越
        if len(self.trajectory_buffer) < 2:
            return

        # 如果没有当前入口ID，无法检测穿越
        if not self.current_entrance_id:
            return

        # 获取当前入口对象
        entrance = self.entrances_by_id.get(self.current_entrance_id)
        if not entrance:
            self.get_logger().warning(f'Cannot find entrance with ID: {self.current_entrance_id}')
            return

        # 获取最近的两个位置点，用于检测穿越
        prev_pos = self.trajectory_buffer[-2].position
        curr_pos = self.trajectory_buffer[-1].position

        # 检查是否穿越了内门线
        inner_crossed = self.check_line_crossing(
            prev_pos, curr_pos,
            entrance.inner_door_line.start, entrance.inner_door_line.end
        )

        # 检查是否穿越了外门线
        outer_crossed = self.check_line_crossing(
            prev_pos, curr_pos,
            entrance.outer_door_line.start, entrance.outer_door_line.end
        )

        # 只在穿越条件满足时打印信息
        if inner_crossed:
            self.get_logger().info(f'内门线穿越: True, 当前状态: {self.current_state.value}')

        if outer_crossed:
            self.get_logger().info(f'外门线穿越: True, 当前状态: {self.current_state.value}')

        # 根据当前状态和穿越情况更新环境状态
        state_changed = self.update_environment_state(inner_crossed, outer_crossed, entrance)

        # 如果状态发生变化，保存环境状态到文件
        if state_changed:
            self.save_environment_state()

            # 发布环境信息
            env_info = EnvInfo()
            env_info.environment = self.current_environment
            env_info.area_id = self.current_area_id
            env_info.entrance_id = self.current_entrance_id
            env_info.changed = True  # 设置状态变化标志
            self.env_info_pub.publish(env_info)

    def check_line_crossing(self, p1, p2, line_start, line_end):
        """检查线段p1p2是否与线段line_start-line_end相交。

        Args:
            p1: 线段1的起点
            p2: 线段1的终点
            line_start: 线段2的起点
            line_end: 线段2的终点

        Returns:
            bool: 如果两线段相交，返回True；否则返回False
        """
        # 计算线段的方向向量
        v1_x = p2.x - p1.x
        v1_y = p2.y - p1.y
        v2_x = line_end.x - line_start.x
        v2_y = line_end.y - line_start.y

        # 计算叉积
        cross_product = v1_x * v2_y - v1_y * v2_x

        # 如果叉积为0，则两线段平行或共线
        if abs(cross_product) < 1e-10:
            return False

        # 计算参数t1和t2
        t1 = ((line_start.x - p1.x) * v2_y - (line_start.y - p1.y) * v2_x) / cross_product
        t2 = ((p1.x - line_start.x) * v1_y - (p1.y - line_start.y) * v1_x) / (-cross_product)

        # 如果t1和t2都在[0,1]范围内，则两线段相交
        is_crossing = 0 <= t1 <= 1 and 0 <= t2 <= 1

        return is_crossing

    def update_environment_state(self, inner_crossed, outer_crossed, entrance):
        """根据门线穿越情况更新环境状态。

        Args:
            inner_crossed: 是否穿越了内门线
            outer_crossed: 是否穿越了外门线
            entrance: 当前入口对象

        Returns:
            bool: 如果状态发生变化，返回True；否则返回False
        """
        # 保存旧状态，用于检测状态是否发生变化
        old_state = self.current_state

        # 根据当前状态和穿越情况更新环境状态
        if self.current_state == EnvironmentState.OUTDOOR:
            # 室外状态下，穿越外门线表示正在进入室内
            if outer_crossed:
                self.current_state = EnvironmentState.ENTERING_INDOOR
                self.get_logger().info(f'【状态转换】: OUTDOOR -> ENTERING_INDOOR (穿越外门线)')
            # 室外状态下，如果穿越内门线（可能是超时后继续移动）
            elif inner_crossed:
                self.current_state = EnvironmentState.INDOOR
                self.get_logger().info(f'【状态转换】: OUTDOOR -> INDOOR (直接穿越内门线)')

        elif self.current_state == EnvironmentState.ENTERING_INDOOR:
            # 正在进入室内状态下，穿越内门线表示已进入室内
            if inner_crossed:
                self.current_state = EnvironmentState.INDOOR
                self.get_logger().info(f'【状态转换】: ENTERING_INDOOR -> INDOOR (穿越内门线)')
            # 正在进入室内状态下，再次穿越外门线表示返回室外
            elif outer_crossed:
                self.current_state = EnvironmentState.OUTDOOR
                self.get_logger().info(f'【状态转换】: ENTERING_INDOOR -> OUTDOOR (再次穿越外门线)')

        elif self.current_state == EnvironmentState.INDOOR:
            # 室内状态下，穿越内门线表示正在离开室内
            if inner_crossed:
                self.current_state = EnvironmentState.EXITING_INDOOR
                self.get_logger().info(f'【状态转换】: INDOOR -> EXITING_INDOOR (穿越内门线)')
            # 室内状态下，如果穿越外门线（可能是超时后继续移动）
            elif outer_crossed:
                self.current_state = EnvironmentState.OUTDOOR
                self.get_logger().info(f'【状态转换】: INDOOR -> OUTDOOR (直接穿越外门线)')

        elif self.current_state == EnvironmentState.EXITING_INDOOR:
            # 正在离开室内状态下，穿越外门线表示已离开室内
            if outer_crossed:
                self.current_state = EnvironmentState.OUTDOOR
                self.get_logger().info(f'【状态转换】: EXITING_INDOOR -> OUTDOOR (穿越外门线)')
            # 正在离开室内状态下，再次穿越内门线表示返回室内
            elif inner_crossed:
                self.current_state = EnvironmentState.INDOOR
                self.get_logger().info(f'【状态转换】: EXITING_INDOOR -> INDOOR (再次穿越内门线)')

        # 更新当前环境变量，用于发布
        self.current_environment = self.current_state.value

        # 检查状态是否发生变化
        state_changed = old_state != self.current_state
        if state_changed:
            self.get_logger().info(f'环境状态已更新: {old_state.value} -> {self.current_state.value}')

        # 返回状态是否发生变化
        return state_changed

    def check_position_change(self):
        """检查位置是否发生显著变化（上帝模式）。"""
        if not self.gps_available or self.last_gps_msg is None or self.last_saved_position is None:
            return False

        # 计算当前位置与上次保存位置的距离
        distance = self.calculate_gps_distance(
            self.last_gps_msg.latitude, self.last_gps_msg.longitude,
            self.last_saved_position[0], self.last_saved_position[1]
        )

        # 如果距离超过阈值，认为位置发生了显著变化
        if distance > self.position_change_threshold:
            self.get_logger().warning(f'Significant position change detected: {distance:.2f}m > {self.position_change_threshold:.2f}m')
            return True

        return False

    def check_environment(self):
        """定期检查环境类型，使用状态机逻辑处理环境切换。"""
        # 如果检测到位置显著变化，重置环境状态
        if self.check_position_change():
            self.get_logger().info('Forcing environment re-detection due to significant position change')
            self.current_state = EnvironmentState.UNKNOWN
            self.current_area_id = ''
            self.current_entrance_id = ''
            self.current_environment = self.current_state.value

        # 如果是首次运行且设置了强制检查，重置环境状态
        if self.force_check_on_startup and self.current_state != EnvironmentState.UNKNOWN:
            self.force_check_on_startup = False  # 只在第一次检查时强制重新检测
            self.get_logger().info('Forcing environment re-detection on startup')
            self.current_state = EnvironmentState.UNKNOWN
            self.current_area_id = ''
            self.current_entrance_id = ''
            self.current_environment = self.current_state.value

        # 未知状态初始化为室外
        if self.current_state == EnvironmentState.UNKNOWN:
            self.current_state = EnvironmentState.OUTDOOR
            self.current_environment = self.current_state.value
            self.get_logger().info('Initializing unknown environment as outdoor')

        # 检查是否在出入口附近
        area_id, entrance_id = self.check_entrance_proximity()

        if area_id and entrance_id:
            # 如果当前没有入口ID，或者入口ID发生变化，更新入口ID和区域ID
            if not self.current_entrance_id or self.current_entrance_id != entrance_id:
                self.current_entrance_id = entrance_id
                self.current_area_id = area_id
                self.get_logger().info(f'Updated current entrance to: {entrance_id} (area: {area_id})')

        # 发布环境信息（状态未变化时的定期发布）
        env_info = EnvInfo()
        env_info.environment = self.current_environment
        env_info.area_id = self.current_area_id
        env_info.entrance_id = self.current_entrance_id
        env_info.changed = False  # 设置状态变化标志为False，因为这是定期发布
        self.env_info_pub.publish(env_info)

    def set_indoor_environment_callback(self, request, response):
        """手动设置室内环境的服务回调。"""
        try:
            if request.data:
                # 保存旧状态
                old_state = self.current_state
                old_environment = self.current_environment

                # 设置为室内环境
                self.current_state = EnvironmentState.INDOOR
                self.current_environment = self.current_state.value

                # 保存状态到文件
                self.save_environment_state()

                # 发布环境信息
                env_info = EnvInfo()
                env_info.environment = self.current_environment
                env_info.area_id = self.current_area_id
                env_info.entrance_id = self.current_entrance_id
                env_info.changed = True  # 设置状态变化标志
                self.env_info_pub.publish(env_info)

                self.get_logger().info(f'手动设置环境状态: {old_environment} -> {self.current_environment}')
                response.success = True
                response.message = f'Environment manually set to indoor'
            else:
                response.success = False
                response.message = 'Request data is False, no action taken'

        except Exception as e:
            self.get_logger().error(f'Failed to set indoor environment: {e}')
            response.success = False
            response.message = f'Error: {str(e)}'

        return response

    def set_outdoor_environment_callback(self, request, response):
        """手动设置室外环境的服务回调。"""
        try:
            if request.data:
                # 保存旧状态
                old_state = self.current_state
                old_environment = self.current_environment

                # 设置为室外环境
                self.current_state = EnvironmentState.OUTDOOR
                self.current_environment = self.current_state.value

                # 清除区域和入口ID
                self.current_area_id = ''
                self.current_entrance_id = ''

                # 保存状态到文件
                self.save_environment_state()

                # 发布环境信息
                env_info = EnvInfo()
                env_info.environment = self.current_environment
                env_info.area_id = self.current_area_id
                env_info.entrance_id = self.current_entrance_id
                env_info.changed = True  # 设置状态变化标志
                self.env_info_pub.publish(env_info)

                self.get_logger().info(f'手动设置环境状态: {old_environment} -> {self.current_environment}')
                response.success = True
                response.message = f'Environment manually set to outdoor'
            else:
                response.success = False
                response.message = 'Request data is False, no action taken'

        except Exception as e:
            self.get_logger().error(f'Failed to set outdoor environment: {e}')
            response.success = False
            response.message = f'Error: {str(e)}'

        return response


def main(args=None):
    """主函数。"""
    rclpy.init(args=args)
    node = EnvironmentDetectorNode()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
